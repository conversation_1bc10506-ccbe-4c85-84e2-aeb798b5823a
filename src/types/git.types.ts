export interface GitCommit {
  hash: string;
  shortHash: string;
  message: string;
  author: {
    name: string;
    email: string;
  };
  date: string;
  refs?: string;
  parents?: string[];
}

export interface GitBranch {
  name: string;
  current: boolean;
  commit: string;
  label?: string;
}

export interface GitRemote {
  name: string;
  refs: {
    fetch: string;
    push: string;
  };
}

export interface GitStatus {
  ahead: number;
  behind: number;
  staged: string[];
  modified: string[];
  not_added: string[];
  conflicted: string[];
  created: string[];
  deleted: string[];
  renamed: string[];
}

export interface GitRepository {
  path: string;
  currentBranch: string;
  branches: string[];
  remotes: GitRemote[];
  status: GitStatus;
}

export interface CommitGraphNode {
  commit: GitCommit;
  x: number;
  y: number;
  color: string;
  connections: {
    to: string;
    points: { x: number; y: number }[];
  }[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CommitDetailsResponse {
  hash: string;
  details: string;
  diff: string;
  files: {
    name: string;
    status: 'A' | 'M' | 'D' | 'R' | 'C';
    additions?: number;
    deletions?: number;
  }[];
}

export interface RepositoryAnalysisRequest {
  repositoryPath: string;
}

export interface CommitsRequest {
  repositoryPath: string;
  branch?: string;
  limit?: number;
  offset?: number;
}

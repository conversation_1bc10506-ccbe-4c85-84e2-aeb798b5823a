/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007acc;
    --primary-hover: #005a9e;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: #ffd700;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Repository Info Bar */
.repo-info {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.repo-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.repo-path {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.repo-path i {
    color: var(--primary-color);
}

.repo-stats {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.stat i {
    width: 16px;
    text-align: center;
}

.repo-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.branch-selector {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.9rem;
    min-width: 200px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-secondary.active {
    background: var(--primary-color);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-ghost:hover {
    background: var(--surface-color);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Welcome Screen */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.welcome-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.feature i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* Loading Screen */
.loading-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.loading-content {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Screen */
.error-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.error-content {
    text-align: center;
    max-width: 500px;
}

.error-content i {
    font-size: 3rem;
    color: var(--danger-color);
    margin-bottom: 1rem;
}

.error-content h3 {
    color: var(--danger-color);
    margin-bottom: 1rem;
}

/* Commit Timeline */
.commit-timeline {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.timeline-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.timeline-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.view-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.view-toggle .btn {
    border-radius: 0;
    border: none;
}

.view-toggle .btn:first-child {
    border-right: 1px solid var(--border-color);
}

/* Commits Container */
.commits-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.commit-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
}

.commit-item:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
    border-color: var(--primary-color);
}

.commit-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.commit-content {
    flex: 1;
    min-width: 0;
}

.commit-message {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.commit-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.commit-hash {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: var(--surface-color);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.commit-refs {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
    flex-shrink: 0;
}

.commit-ref {
    background: var(--info-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.commit-ref.branch {
    background: var(--success-color);
}

.commit-ref.tag {
    background: var(--warning-color);
    color: var(--dark-color);
}

/* Commit Graph */
.commit-graph {
    position: relative;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
}

#commitCanvas {
    display: block;
    cursor: pointer;
}

/* Commit Details Sidebar */
.commit-details-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: right 0.3s ease;
    z-index: 200;
    display: flex;
    flex-direction: column;
}

.commit-details-sidebar.open {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--surface-color);
}

.sidebar-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.commit-detail-section {
    margin-bottom: 1.5rem;
}

.commit-detail-section h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.commit-detail-info {
    background: var(--surface-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.commit-detail-info .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.commit-detail-info .detail-row:last-child {
    margin-bottom: 0;
}

.commit-detail-info .detail-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.commit-files {
    list-style: none;
}

.commit-files li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.25rem;
}

.commit-files li:hover {
    background: var(--surface-color);
}

.file-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    color: white;
}

.file-status.added { background: var(--success-color); }
.file-status.modified { background: var(--warning-color); color: var(--dark-color); }
.file-status.deleted { background: var(--danger-color); }

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success { border-left: 4px solid var(--success-color); }
.toast.error { border-left: 4px solid var(--danger-color); }
.toast.warning { border-left: 4px solid var(--warning-color); }
.toast.info { border-left: 4px solid var(--info-color); }

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .repo-info {
        padding: 1rem;
        flex-direction: column;
        align-items: stretch;
    }
    
    .repo-stats {
        justify-content: space-between;
    }
    
    .commit-timeline {
        padding: 1rem;
    }
    
    .timeline-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .timeline-controls {
        justify-content: space-between;
    }
    
    .commit-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .commit-meta {
        justify-content: space-between;
    }
    
    .commit-details-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

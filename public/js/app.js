(()=>{"use strict";(()=>{class t{constructor(){this.currentRepository=null,this.currentCommits=[],this.currentBranch="HEAD",this.isLoading=!1,this.commitOffset=0,this.commitLimit=50,this.initializeEventListeners(),this.showWelcomeScreen()}initializeEventListeners(){const t=document.getElementById("selectRepoBtn"),e=document.getElementById("folderInput");t?.addEventListener("click",(()=>{e?.click()})),e?.addEventListener("change",(t=>{const e=t.target;if(e.files&&e.files.length>0){const t=e.files[0].webkitRelativePath.split("/")[0],n=e.files[0].webkitRelativePath.replace(t+"/",""),s=e.files[0].path?.replace("/"+n,"")||t;this.analyzeRepository(s)}}));const n=document.getElementById("branchSelector");n?.addEventListener("change",(t=>{const e=t.target;e.value&&this.currentRepository&&(this.currentBranch=e.value,this.loadCommits(!0))}));const s=document.getElementById("refreshBtn");s?.addEventListener("click",(()=>{this.currentRepository&&this.analyzeRepository(this.currentRepository.path)}));const o=document.getElementById("listViewBtn"),i=document.getElementById("graphViewBtn");o?.addEventListener("click",(()=>{this.switchToListView()})),i?.addEventListener("click",(()=>{this.switchToGraphView()}));const a=document.getElementById("loadMoreBtn");a?.addEventListener("click",(()=>{this.loadMoreCommits()}));const r=document.getElementById("closeSidebarBtn");r?.addEventListener("click",(()=>{this.closeSidebar()}));const c=document.getElementById("retryBtn");c?.addEventListener("click",(()=>{this.showWelcomeScreen()}))}showWelcomeScreen(){this.hideAllScreens();const t=document.getElementById("welcomeScreen");t&&(t.style.display="flex")}showLoadingScreen(t="Analizando repositorio..."){this.hideAllScreens();const e=document.getElementById("loadingScreen"),n=document.getElementById("loadingText");e&&(e.style.display="flex"),n&&(n.textContent=t)}showErrorScreen(t){this.hideAllScreens();const e=document.getElementById("errorScreen"),n=document.getElementById("errorMessage");e&&(e.style.display="flex"),n&&(n.textContent=t)}showCommitTimeline(){this.hideAllScreens();const t=document.getElementById("commitTimeline");t&&(t.style.display="block")}hideAllScreens(){["welcomeScreen","loadingScreen","errorScreen","commitTimeline"].forEach((t=>{const e=document.getElementById(t);e&&(e.style.display="none")}))}async analyzeRepository(t){if(!this.isLoading){this.isLoading=!0,this.showLoadingScreen("Analizando repositorio...");try{const e=await fetch("/api/repository/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({repositoryPath:t})}),n=await e.json();if(!n.success||!n.data)throw new Error(n.error||"Failed to analyze repository");this.currentRepository=n.data,this.updateRepositoryInfo(),await this.loadCommits(!0)}catch(t){console.error("Error analyzing repository:",t),this.showErrorScreen(t instanceof Error?t.message:"Error desconocido")}finally{this.isLoading=!1}}}updateRepositoryInfo(){if(!this.currentRepository)return;const t=document.getElementById("repoInfo");t&&(t.style.display="flex");const e=document.getElementById("repoPath");e&&(e.textContent=this.currentRepository.path,e.setAttribute("title",this.currentRepository.path));const n=document.getElementById("currentBranch");n&&(n.textContent=this.currentRepository.currentBranch);const s=document.getElementById("repoStatus");if(s){const t=s.querySelector("span"),e=s.querySelector("i");this.currentRepository.status.modified.length>0||this.currentRepository.status.not_added.length>0?(t&&(t.textContent="Modificado"),e&&(e.className="fas fa-circle",e.style.color="var(--warning-color)")):(t&&(t.textContent="Limpio"),e&&(e.className="fas fa-circle",e.style.color="var(--success-color)"))}this.updateBranchSelector()}updateBranchSelector(){if(!this.currentRepository)return;const t=document.getElementById("branchSelector");t&&(t.innerHTML='<option value="">Seleccionar rama...</option>',this.currentRepository.branches.forEach((e=>{const n=document.createElement("option");n.value=e,n.textContent=e,e===this.currentRepository.currentBranch&&(n.selected=!0,this.currentBranch=e),t.appendChild(n)})))}async loadCommits(t=!1){if(this.currentRepository&&!this.isLoading){t&&(this.commitOffset=0,this.currentCommits=[]),this.isLoading=!0,this.showLoadingScreen("Cargando commits...");try{const e=await fetch("/api/repository/commits",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({repositoryPath:this.currentRepository.path,branch:this.currentBranch,limit:this.commitLimit,offset:this.commitOffset})}),n=await e.json();if(!n.success||!n.data)throw new Error(n.error||"Failed to load commits");t?this.currentCommits=n.data.commits:this.currentCommits.push(...n.data.commits),this.commitOffset+=n.data.commits.length,this.updateCommitCount(),this.renderCommits(),this.showCommitTimeline()}catch(t){console.error("Error loading commits:",t),this.showErrorScreen(t instanceof Error?t.message:"Error cargando commits")}finally{this.isLoading=!1}}}async loadMoreCommits(){await this.loadCommits(!1)}updateCommitCount(){const t=document.getElementById("commitCount");if(t){const e=this.currentCommits.length;t.textContent=`${e} commit${1!==e?"s":""}`}}renderCommits(){const t=document.getElementById("commitListView");t&&(t.innerHTML="",this.currentCommits.forEach((e=>{const n=this.createCommitElement(e);t.appendChild(n)})))}createCommitElement(t){const e=document.createElement("div");e.className="commit-item",e.addEventListener("click",(()=>this.showCommitDetails(t.hash)));const n=document.createElement("div");n.className="commit-avatar",n.textContent=t.author.name.charAt(0).toUpperCase(),n.style.backgroundColor=this.getAvatarColor(t.author.email);const s=document.createElement("div");s.className="commit-content";const o=document.createElement("div");o.className="commit-message",o.textContent=t.message;const i=document.createElement("div");i.className="commit-meta";const a=document.createElement("span");a.textContent=t.author.name;const r=document.createElement("span");r.textContent=this.formatDate(t.date);const c=document.createElement("span");if(c.className="commit-hash",c.textContent=t.shortHash,i.appendChild(a),i.appendChild(r),i.appendChild(c),s.appendChild(o),s.appendChild(i),e.appendChild(n),e.appendChild(s),t.refs){const n=document.createElement("div");n.className="commit-refs",t.refs.split(", ").forEach((t=>{if(t.trim()){const e=document.createElement("span");e.className="commit-ref",t.includes("origin/")?(e.classList.add("branch"),e.textContent=t.replace("origin/","")):t.includes("tag:")?(e.classList.add("tag"),e.textContent=t.replace("tag: ","")):e.textContent=t,n.appendChild(e)}})),n.children.length>0&&e.appendChild(n)}return e}getAvatarColor(t){const e=["#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#feca57","#ff9ff3","#54a0ff","#5f27cd"];let n=0;for(let e=0;e<t.length;e++)n=t.charCodeAt(e)+((n<<5)-n);return e[Math.abs(n)%e.length]}formatDate(t){const e=new Date(t),n=(new Date).getTime()-e.getTime(),s=Math.floor(n/864e5);return 0===s?"Hoy":1===s?"Ayer":s<7?`Hace ${s} días`:e.toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric"})}async showCommitDetails(t){if(this.currentRepository)try{const e=await fetch(`/api/repository/commit/${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({repositoryPath:this.currentRepository.path})}),n=await e.json();if(!n.success||!n.data)throw new Error(n.error||"Failed to load commit details");this.renderCommitDetails(n.data),this.openSidebar()}catch(t){console.error("Error loading commit details:",t),this.showToast("Error cargando detalles del commit","error")}}renderCommitDetails(t){const e=document.getElementById("commitDetailsContent");if(!e)return;const n=this.currentCommits.find((e=>e.hash===t.hash));n&&(e.innerHTML=`\n      <div class="commit-detail-section">\n        <div class="commit-detail-info">\n          <div class="detail-row">\n            <span class="detail-label">Hash:</span>\n            <span class="commit-hash">${n.shortHash}</span>\n          </div>\n          <div class="detail-row">\n            <span class="detail-label">Autor:</span>\n            <span>${n.author.name}</span>\n          </div>\n          <div class="detail-row">\n            <span class="detail-label">Email:</span>\n            <span>${n.author.email}</span>\n          </div>\n          <div class="detail-row">\n            <span class="detail-label">Fecha:</span>\n            <span>${new Date(n.date).toLocaleString("es-ES")}</span>\n          </div>\n        </div>\n      </div>\n\n      <div class="commit-detail-section">\n        <h4><i class="fas fa-comment"></i> Mensaje</h4>\n        <p>${n.message}</p>\n      </div>\n\n      <div class="commit-detail-section">\n        <h4><i class="fas fa-file-alt"></i> Archivos modificados</h4>\n        <ul class="commit-files">\n          ${t.files.map((t=>`\n            <li>\n              <span class="file-status ${t.status.toLowerCase()}">${this.getFileStatusIcon(t.status)}</span>\n              <span>${t.name}</span>\n            </li>\n          `)).join("")}\n        </ul>\n      </div>\n    `)}getFileStatusIcon(t){switch(t){case"A":return"+";case"M":return"~";case"D":return"-";case"R":return"→";case"C":return"©";default:return"?"}}switchToListView(){const t=document.getElementById("listViewBtn"),e=document.getElementById("graphViewBtn"),n=document.getElementById("commitListView"),s=document.getElementById("commitGraphView");t?.classList.add("active"),e?.classList.remove("active"),n&&(n.style.display="block"),s&&(s.style.display="none")}switchToGraphView(){const t=document.getElementById("listViewBtn"),e=document.getElementById("graphViewBtn"),n=document.getElementById("commitListView"),s=document.getElementById("commitGraphView");t?.classList.remove("active"),e?.classList.add("active"),n&&(n.style.display="none"),s&&(s.style.display="block"),this.renderCommitGraph()}renderCommitGraph(){const t=document.getElementById("commitCanvas");if(!t)return;const e=t.getContext("2d");e&&(t.width=t.offsetWidth,t.height=Math.max(600,60*this.currentCommits.length),e.clearRect(0,0,t.width,t.height),this.currentCommits.forEach(((t,n)=>{const s=60*n+30;e.beginPath(),e.arc(50,s,8,0,2*Math.PI),e.fillStyle=this.getAvatarColor(t.author.email),e.fill(),e.strokeStyle="#fff",e.lineWidth=2,e.stroke(),n<this.currentCommits.length-1&&(e.beginPath(),e.moveTo(50,s+8),e.lineTo(50,s+52),e.strokeStyle="#dee2e6",e.lineWidth=2,e.stroke()),e.fillStyle="#212529",e.font='14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',e.fillText(t.message,70,s-5),e.fillStyle="#6c757d",e.font='12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',e.fillText(`${t.author.name} • ${t.shortHash}`,70,s+15)})),t.onclick=e=>{const n=t.getBoundingClientRect(),s=e.clientY-n.top,o=Math.floor((s-30)/60);o>=0&&o<this.currentCommits.length&&this.showCommitDetails(this.currentCommits[o].hash)})}openSidebar(){const t=document.getElementById("commitDetailsSidebar");t&&(t.style.display="flex",setTimeout((()=>{t.classList.add("open")}),10))}closeSidebar(){const t=document.getElementById("commitDetailsSidebar");t&&(t.classList.remove("open"),setTimeout((()=>{t.style.display="none"}),300))}showToast(t,e="info"){const n=document.getElementById("toastContainer");if(!n)return;const s=document.createElement("div");s.className=`toast ${e}`,s.textContent=t,n.appendChild(s),setTimeout((()=>{s.remove()}),5e3)}}document.addEventListener("DOMContentLoaded",(()=>{new t}))})()})();
//# sourceMappingURL=app.js.map
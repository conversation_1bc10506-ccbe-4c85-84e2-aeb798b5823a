{"version": 3, "file": "app.js", "mappings": "yBAOA,MAAMA,EAQJ,WAAAC,GAPQ,KAAAC,kBAA0C,KAC1C,KAAAC,eAA8B,GAC9B,KAAAC,cAAwB,OACxB,KAAAC,WAAqB,EACrB,KAAAC,aAAuB,EACd,KAAAC,YAAsB,GAGrCC,KAAKC,2BACLD,KAAKE,mBACP,CAEQ,wBAAAD,GAEN,MAAME,EAAgBC,SAASC,eAAe,iBACxCC,EAAcF,SAASC,eAAe,eAE5CF,GAAeI,iBAAiB,SAAS,KACvCD,GAAaE,WAGfF,GAAaC,iBAAiB,UAAWE,IACvC,MAAMC,EAASD,EAAMC,OACrB,GAAIA,EAAOC,OAASD,EAAOC,MAAMC,OAAS,EAAG,CAC3C,MAAMC,EAAaH,EAAOC,MAAM,GAAGG,mBAAmBC,MAAM,KAAK,GAC3DC,EAAWN,EAAOC,MAAM,GAAGG,mBAAmBG,QAAQJ,EAAa,IAAK,IAExEK,EAAkBR,EAAOC,MAAM,GAAWQ,MAAMF,QAAQ,IAAMD,EAAU,KAAOH,EACrFb,KAAKoB,kBAAkBF,EACzB,KAIF,MAAMG,EAAiBjB,SAASC,eAAe,kBAC/CgB,GAAgBd,iBAAiB,UAAWE,IAC1C,MAAMC,EAASD,EAAMC,OACjBA,EAAOY,OAAStB,KAAKN,oBACvBM,KAAKJ,cAAgBc,EAAOY,MAC5BtB,KAAKuB,aAAY,OAKrB,MAAMC,EAAapB,SAASC,eAAe,cAC3CmB,GAAYjB,iBAAiB,SAAS,KAChCP,KAAKN,mBACPM,KAAKoB,kBAAkBpB,KAAKN,kBAAkByB,SAKlD,MAAMM,EAAcrB,SAASC,eAAe,eACtCqB,EAAetB,SAASC,eAAe,gBAE7CoB,GAAalB,iBAAiB,SAAS,KACrCP,KAAK2B,sBAGPD,GAAcnB,iBAAiB,SAAS,KACtCP,KAAK4B,uBAIP,MAAMC,EAAczB,SAASC,eAAe,eAC5CwB,GAAatB,iBAAiB,SAAS,KACrCP,KAAK8B,qBAIP,MAAMC,EAAkB3B,SAASC,eAAe,mBAChD0B,GAAiBxB,iBAAiB,SAAS,KACzCP,KAAKgC,kBAIP,MAAMC,EAAW7B,SAASC,eAAe,YACzC4B,GAAU1B,iBAAiB,SAAS,KAClCP,KAAKE,sBAET,CAEQ,iBAAAA,GACNF,KAAKkC,iBACL,MAAMC,EAAgB/B,SAASC,eAAe,iBAC1C8B,IACFA,EAAcC,MAAMC,QAAU,OAElC,CAEQ,iBAAAC,CAAkBC,EAAkB,6BAC1CvC,KAAKkC,iBACL,MAAMM,EAAgBpC,SAASC,eAAe,iBACxCoC,EAAcrC,SAASC,eAAe,eAExCmC,IACFA,EAAcJ,MAAMC,QAAU,QAG5BI,IACFA,EAAYC,YAAcH,EAE9B,CAEQ,eAAAI,CAAgBJ,GACtBvC,KAAKkC,iBACL,MAAMU,EAAcxC,SAASC,eAAe,eACtCwC,EAAezC,SAASC,eAAe,gBAEzCuC,IACFA,EAAYR,MAAMC,QAAU,QAG1BQ,IACFA,EAAaH,YAAcH,EAE/B,CAEQ,kBAAAO,GACN9C,KAAKkC,iBACL,MAAMa,EAAiB3C,SAASC,eAAe,kBAC3C0C,IACFA,EAAeX,MAAMC,QAAU,QAEnC,CAEQ,cAAAH,GACU,CACd,gBACA,gBACA,cACA,kBAGMc,SAAQC,IACd,MAAMC,EAAS9C,SAASC,eAAe4C,GACnCC,IACFA,EAAOd,MAAMC,QAAU,UAG7B,CAEQ,uBAAMjB,CAAkBF,GAC9B,IAAIlB,KAAKH,UAAT,CAEAG,KAAKH,WAAY,EACjBG,KAAKsC,kBAAkB,6BAEvB,IACE,MAAMa,QAAiBC,MAAM,0BAA2B,CACtDC,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAU,CAAEvC,qBAGnBwC,QAA2CP,EAASQ,OAE1D,IAAKD,EAAOE,UAAYF,EAAOG,KAC7B,MAAM,IAAIC,MAAMJ,EAAOK,OAAS,gCAGlC/D,KAAKN,kBAAoBgE,EAAOG,KAChC7D,KAAKgE,6BACChE,KAAKuB,aAAY,EAEzB,CAAE,MAAOwC,GACPE,QAAQF,MAAM,8BAA+BA,GAC7C/D,KAAK2C,gBAAgBoB,aAAiBD,MAAQC,EAAMxB,QAAU,oBAChE,C,QACEvC,KAAKH,WAAY,CACnB,CA7B0B,CA8B5B,CAEQ,oBAAAmE,GACN,IAAKhE,KAAKN,kBAAmB,OAG7B,MAAMwE,EAAW9D,SAASC,eAAe,YACrC6D,IACFA,EAAS9B,MAAMC,QAAU,QAI3B,MAAM8B,EAAW/D,SAASC,eAAe,YACrC8D,IACFA,EAASzB,YAAc1C,KAAKN,kBAAkByB,KAC9CgD,EAASC,aAAa,QAASpE,KAAKN,kBAAkByB,OAIxD,MAAMvB,EAAgBQ,SAASC,eAAe,iBAC1CT,IACFA,EAAc8C,YAAc1C,KAAKN,kBAAkBE,eAIrD,MAAMyE,EAAajE,SAASC,eAAe,cAC3C,GAAIgE,EAAY,CACd,MAAMC,EAAaD,EAAWE,cAAc,QACtCC,EAAaH,EAAWE,cAAc,KAExCvE,KAAKN,kBAAkB+E,OAAOC,SAAS9D,OAAS,GAChDZ,KAAKN,kBAAkB+E,OAAOE,UAAU/D,OAAS,GAC/C0D,IAAYA,EAAW5B,YAAc,cACrC8B,IACFA,EAAWI,UAAY,gBACvBJ,EAAWpC,MAAMyC,MAAQ,0BAGvBP,IAAYA,EAAW5B,YAAc,UACrC8B,IACFA,EAAWI,UAAY,gBACvBJ,EAAWpC,MAAMyC,MAAQ,wBAG/B,CAGA7E,KAAK8E,sBACP,CAEQ,oBAAAA,GACN,IAAK9E,KAAKN,kBAAmB,OAE7B,MAAM2B,EAAiBjB,SAASC,eAAe,kBAC1CgB,IAGLA,EAAe0D,UAAY,gDAG3B/E,KAAKN,kBAAkBsF,SAAShC,SAAQiC,IACtC,MAAMC,EAAS9E,SAAS+E,cAAc,UACtCD,EAAO5D,MAAQ2D,EACfC,EAAOxC,YAAcuC,EAEjBA,IAAWjF,KAAKN,kBAAmBE,gBACrCsF,EAAOE,UAAW,EAClBpF,KAAKJ,cAAgBqF,GAGvB5D,EAAegE,YAAYH,MAE/B,CAEQ,iBAAM3D,CAAY+D,GAAiB,GACzC,GAAKtF,KAAKN,oBAAqBM,KAAKH,UAApC,CAEIyF,IACFtF,KAAKF,aAAe,EACpBE,KAAKL,eAAiB,IAGxBK,KAAKH,WAAY,EACjBG,KAAKsC,kBAAkB,uBAEvB,IACE,MAAMa,QAAiBC,MAAM,0BAA2B,CACtDC,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAU,CACnBvC,eAAgBlB,KAAKN,kBAAkByB,KACvC8D,OAAQjF,KAAKJ,cACb2F,MAAOvF,KAAKD,YACZyF,OAAQxF,KAAKF,iBAIX4D,QAAmEP,EAASQ,OAElF,IAAKD,EAAOE,UAAYF,EAAOG,KAC7B,MAAM,IAAIC,MAAMJ,EAAOK,OAAS,0BAG9BuB,EACFtF,KAAKL,eAAiB+D,EAAOG,KAAK4B,QAElCzF,KAAKL,eAAe+F,QAAQhC,EAAOG,KAAK4B,SAG1CzF,KAAKF,cAAgB4D,EAAOG,KAAK4B,QAAQ7E,OACzCZ,KAAK2F,oBACL3F,KAAK4F,gBACL5F,KAAK8C,oBAEP,CAAE,MAAOiB,GACPE,QAAQF,MAAM,yBAA0BA,GACxC/D,KAAK2C,gBAAgBoB,aAAiBD,MAAQC,EAAMxB,QAAU,yBAChE,C,QACEvC,KAAKH,WAAY,CACnB,CA9CqD,CA+CvD,CAEQ,qBAAMiC,SACN9B,KAAKuB,aAAY,EACzB,CAEQ,iBAAAoE,GACN,MAAME,EAAczF,SAASC,eAAe,eAC5C,GAAIwF,EAAa,CACf,MAAMC,EAAQ9F,KAAKL,eAAeiB,OAClCiF,EAAYnD,YAAc,GAAGoD,WAAyB,IAAVA,EAAc,IAAM,IAClE,CACF,CAEQ,aAAAF,GACN,MAAMG,EAAY3F,SAASC,eAAe,kBACrC0F,IAELA,EAAUhB,UAAY,GAEtB/E,KAAKL,eAAeqD,SAAQgD,IAC1B,MAAMC,EAAgBjG,KAAKkG,oBAAoBF,GAC/CD,EAAUV,YAAYY,MAE1B,CAEQ,mBAAAC,CAAoBF,GAC1B,MAAMG,EAAa/F,SAAS+E,cAAc,OAC1CgB,EAAWvB,UAAY,cACvBuB,EAAW5F,iBAAiB,SAAS,IAAMP,KAAKoG,kBAAkBJ,EAAOK,QAEzE,MAAMC,EAASlG,SAAS+E,cAAc,OACtCmB,EAAO1B,UAAY,gBACnB0B,EAAO5D,YAAcsD,EAAOO,OAAOC,KAAKC,OAAO,GAAGC,cAClDJ,EAAOlE,MAAMuE,gBAAkB3G,KAAK4G,eAAeZ,EAAOO,OAAOM,OAEjE,MAAMC,EAAU1G,SAAS+E,cAAc,OACvC2B,EAAQlC,UAAY,iBAEpB,MAAMrC,EAAUnC,SAAS+E,cAAc,OACvC5C,EAAQqC,UAAY,iBACpBrC,EAAQG,YAAcsD,EAAOzD,QAE7B,MAAMwE,EAAO3G,SAAS+E,cAAc,OACpC4B,EAAKnC,UAAY,cAEjB,MAAM2B,EAASnG,SAAS+E,cAAc,QACtCoB,EAAO7D,YAAcsD,EAAOO,OAAOC,KAEnC,MAAMQ,EAAO5G,SAAS+E,cAAc,QACpC6B,EAAKtE,YAAc1C,KAAKiH,WAAWjB,EAAOgB,MAE1C,MAAMX,EAAOjG,SAAS+E,cAAc,QAepC,GAdAkB,EAAKzB,UAAY,cACjByB,EAAK3D,YAAcsD,EAAOkB,UAE1BH,EAAK1B,YAAYkB,GACjBQ,EAAK1B,YAAY2B,GACjBD,EAAK1B,YAAYgB,GAEjBS,EAAQzB,YAAY9C,GACpBuE,EAAQzB,YAAY0B,GAEpBZ,EAAWd,YAAYiB,GACvBH,EAAWd,YAAYyB,GAGnBd,EAAOmB,KAAM,CACf,MAAMA,EAAO/G,SAAS+E,cAAc,OACpCgC,EAAKvC,UAAY,cAEAoB,EAAOmB,KAAKpG,MAAM,MAC1BiC,SAAQoE,IACf,GAAIA,EAAIC,OAAQ,CACd,MAAMC,EAAalH,SAAS+E,cAAc,QAC1CmC,EAAW1C,UAAY,aAEnBwC,EAAIG,SAAS,YACfD,EAAWE,UAAUC,IAAI,UACzBH,EAAW5E,YAAc0E,EAAInG,QAAQ,UAAW,KACvCmG,EAAIG,SAAS,SACtBD,EAAWE,UAAUC,IAAI,OACzBH,EAAW5E,YAAc0E,EAAInG,QAAQ,QAAS,KAE9CqG,EAAW5E,YAAc0E,EAG3BD,EAAK9B,YAAYiC,EACnB,KAGEH,EAAKO,SAAS9G,OAAS,GACzBuF,EAAWd,YAAY8B,EAE3B,CAEA,OAAOhB,CACT,CAEQ,cAAAS,CAAeC,GACrB,MAAMc,EAAS,CACb,UAAW,UAAW,UAAW,UACjC,UAAW,UAAW,UAAW,WAGnC,IAAItB,EAAO,EACX,IAAK,IAAIuB,EAAI,EAAGA,EAAIf,EAAMjG,OAAQgH,IAChCvB,EAAOQ,EAAMgB,WAAWD,KAAOvB,GAAQ,GAAKA,GAG9C,OAAOsB,EAAOG,KAAKC,IAAI1B,GAAQsB,EAAO/G,OACxC,CAEQ,UAAAqG,CAAWe,GACjB,MAAMhB,EAAO,IAAIiB,KAAKD,GAEhBE,GADM,IAAID,MACGE,UAAYnB,EAAKmB,UAC9BC,EAAWN,KAAKO,MAAMH,EAAS,OAErC,OAAiB,IAAbE,EACK,MACe,IAAbA,EACF,OACEA,EAAW,EACb,QAAQA,SAERpB,EAAKsB,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,QACPC,IAAK,WAGX,CAEQ,uBAAMrC,CAAkBC,GAC9B,GAAKrG,KAAKN,kBAEV,IACE,MAAMyD,QAAiBC,MAAM,0BAA0BiD,IAAQ,CAC7DhD,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAU,CACnBvC,eAAgBlB,KAAKN,kBAAkByB,SAIrCuC,QAAmDP,EAASQ,OAElE,IAAKD,EAAOE,UAAYF,EAAOG,KAC7B,MAAM,IAAIC,MAAMJ,EAAOK,OAAS,iCAGlC/D,KAAK0I,oBAAoBhF,EAAOG,MAChC7D,KAAK2I,aAEP,CAAE,MAAO5E,GACPE,QAAQF,MAAM,gCAAiCA,GAC/C/D,KAAK4I,UAAU,qCAAsC,QACvD,CACF,CAEQ,mBAAAF,CAAoBG,GAC1B,MAAM/B,EAAU1G,SAASC,eAAe,wBACxC,IAAKyG,EAAS,OAEd,MAAMd,EAAShG,KAAKL,eAAemJ,MAAKC,GAAKA,EAAE1C,OAASwC,EAAQxC,OAC3DL,IAELc,EAAQ/B,UAAY,yNAKkBiB,EAAOkB,mJAI7BlB,EAAOO,OAAOC,8IAIdR,EAAOO,OAAOM,+IAId,IAAIoB,KAAKjC,EAAOgB,MAAMgC,eAAe,qLAO5ChD,EAAOzD,+LAMRsG,EAAQlI,MAAMsI,KAAIC,GAAQ,8DAEIA,EAAKzE,OAAO0E,kBAAmBnJ,KAAKoJ,kBAAkBF,EAAKzE,uCAC/EyE,EAAK1C,+CAEd6C,KAAK,yCAIhB,CAEQ,iBAAAD,CAAkB3E,GACxB,OAAQA,GACN,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,MAAO,IACjB,QAAS,MAAO,IAEpB,CAEQ,gBAAA9C,GACN,MAAMF,EAAcrB,SAASC,eAAe,eACtCqB,EAAetB,SAASC,eAAe,gBACvCiJ,EAAWlJ,SAASC,eAAe,kBACnCkJ,EAAYnJ,SAASC,eAAe,mBAE1CoB,GAAa+F,UAAUC,IAAI,UAC3B/F,GAAc8F,UAAUgC,OAAO,UAE3BF,IAAUA,EAASlH,MAAMC,QAAU,SACnCkH,IAAWA,EAAUnH,MAAMC,QAAU,OAC3C,CAEQ,iBAAAT,GACN,MAAMH,EAAcrB,SAASC,eAAe,eACtCqB,EAAetB,SAASC,eAAe,gBACvCiJ,EAAWlJ,SAASC,eAAe,kBACnCkJ,EAAYnJ,SAASC,eAAe,mBAE1CoB,GAAa+F,UAAUgC,OAAO,UAC9B9H,GAAc8F,UAAUC,IAAI,UAExB6B,IAAUA,EAASlH,MAAMC,QAAU,QACnCkH,IAAWA,EAAUnH,MAAMC,QAAU,SAEzCrC,KAAKyJ,mBACP,CAEQ,iBAAAA,GACN,MAAMC,EAAStJ,SAASC,eAAe,gBACvC,IAAKqJ,EAAQ,OAEb,MAAMC,EAAMD,EAAOE,WAAW,MACzBD,IAGLD,EAAOG,MAAQH,EAAOI,YACtBJ,EAAOK,OAASjC,KAAKkC,IAAI,IAAkC,GAA7BhK,KAAKL,eAAeiB,QAGlD+I,EAAIM,UAAU,EAAG,EAAGP,EAAOG,MAAOH,EAAOK,QAGzC/J,KAAKL,eAAeqD,SAAQ,CAACgD,EAAQkE,KACnC,MAAMC,EAAY,GAARD,EAAa,GAIvBP,EAAIS,YACJT,EAAIU,IAJM,GAICF,EAAG,EAAG,EAAG,EAAIrC,KAAKwC,IAC7BX,EAAIY,UAAYvK,KAAK4G,eAAeZ,EAAOO,OAAOM,OAClD8C,EAAIa,OACJb,EAAIc,YAAc,OAClBd,EAAIe,UAAY,EAChBf,EAAIgB,SAGAT,EAAQlK,KAAKL,eAAeiB,OAAS,IACvC+I,EAAIS,YACJT,EAAIiB,OAdI,GAcMT,EAAI,GAClBR,EAAIkB,OAfI,GAeMV,EAAI,IAClBR,EAAIc,YAAc,UAClBd,EAAIe,UAAY,EAChBf,EAAIgB,UAINhB,EAAIY,UAAY,UAChBZ,EAAImB,KAAO,6DACXnB,EAAIoB,SAAS/E,EAAOzD,QAASyI,GAAQb,EAAI,GAEzCR,EAAIY,UAAY,UAChBZ,EAAImB,KAAO,6DACXnB,EAAIoB,SAAS,GAAG/E,EAAOO,OAAOC,UAAUR,EAAOkB,YAAa8D,GAAQb,EAAI,OAI1ET,EAAOuB,QAAWxK,IAChB,MAAMyK,EAAOxB,EAAOyB,wBACdhB,EAAI1J,EAAM2K,QAAUF,EAAKG,IACzBC,EAAcxD,KAAKO,OAAO8B,EAAI,IAAM,IAEtCmB,GAAe,GAAKA,EAActL,KAAKL,eAAeiB,QACxDZ,KAAKoG,kBAAkBpG,KAAKL,eAAe2L,GAAajF,OAG9D,CAEQ,WAAAsC,GACN,MAAM4C,EAAUnL,SAASC,eAAe,wBACpCkL,IACFA,EAAQnJ,MAAMC,QAAU,OACxBmJ,YAAW,KACTD,EAAQ/D,UAAUC,IAAI,UACrB,IAEP,CAEQ,YAAAzF,GACN,MAAMuJ,EAAUnL,SAASC,eAAe,wBACpCkL,IACFA,EAAQ/D,UAAUgC,OAAO,QACzBgC,YAAW,KACTD,EAAQnJ,MAAMC,QAAU,SACvB,KAEP,CAEQ,SAAAuG,CAAUrG,EAAiBkJ,EAAiD,QAClF,MAAM1F,EAAY3F,SAASC,eAAe,kBAC1C,IAAK0F,EAAW,OAEhB,MAAM2F,EAAQtL,SAAS+E,cAAc,OACrCuG,EAAM9G,UAAY,SAAS6G,IAC3BC,EAAMhJ,YAAcH,EAEpBwD,EAAUV,YAAYqG,GAEtBF,YAAW,KACTE,EAAMlC,WACL,IACL,EAIFpJ,SAASG,iBAAiB,oBAAoB,KAC5C,IAAIf,I", "sources": ["webpack://git-visual-viewer/./src/client/app.ts"], "sourcesContent": ["import { \n  GitRepository, \n  GitCommit, \n  ApiResponse, \n  CommitDetailsResponse \n} from '../types/git.types';\n\nclass GitVisualViewer {\n  private currentRepository: GitRepository | null = null;\n  private currentCommits: GitCommit[] = [];\n  private currentBranch: string = 'HEAD';\n  private isLoading: boolean = false;\n  private commitOffset: number = 0;\n  private readonly commitLimit: number = 50;\n\n  constructor() {\n    this.initializeEventListeners();\n    this.showWelcomeScreen();\n  }\n\n  private initializeEventListeners(): void {\n    // Repository selection\n    const selectRepoBtn = document.getElementById('selectRepoBtn') as HTMLButtonElement;\n    const folderInput = document.getElementById('folderInput') as HTMLInputElement;\n    \n    selectRepoBtn?.addEventListener('click', () => {\n      folderInput?.click();\n    });\n\n    folderInput?.addEventListener('change', (event) => {\n      const target = event.target as HTMLInputElement;\n      if (target.files && target.files.length > 0) {\n        const folderPath = target.files[0].webkitRelativePath.split('/')[0];\n        const fullPath = target.files[0].webkitRelativePath.replace(folderPath + '/', '');\n        // Get the parent directory path\n        const repositoryPath = (target.files[0] as any).path?.replace('/' + fullPath, '') || folderPath;\n        this.analyzeRepository(repositoryPath);\n      }\n    });\n\n    // Branch selector\n    const branchSelector = document.getElementById('branchSelector') as HTMLSelectElement;\n    branchSelector?.addEventListener('change', (event) => {\n      const target = event.target as HTMLSelectElement;\n      if (target.value && this.currentRepository) {\n        this.currentBranch = target.value;\n        this.loadCommits(true);\n      }\n    });\n\n    // Refresh button\n    const refreshBtn = document.getElementById('refreshBtn') as HTMLButtonElement;\n    refreshBtn?.addEventListener('click', () => {\n      if (this.currentRepository) {\n        this.analyzeRepository(this.currentRepository.path);\n      }\n    });\n\n    // View toggle buttons\n    const listViewBtn = document.getElementById('listViewBtn') as HTMLButtonElement;\n    const graphViewBtn = document.getElementById('graphViewBtn') as HTMLButtonElement;\n    \n    listViewBtn?.addEventListener('click', () => {\n      this.switchToListView();\n    });\n    \n    graphViewBtn?.addEventListener('click', () => {\n      this.switchToGraphView();\n    });\n\n    // Load more button\n    const loadMoreBtn = document.getElementById('loadMoreBtn') as HTMLButtonElement;\n    loadMoreBtn?.addEventListener('click', () => {\n      this.loadMoreCommits();\n    });\n\n    // Sidebar close button\n    const closeSidebarBtn = document.getElementById('closeSidebarBtn') as HTMLButtonElement;\n    closeSidebarBtn?.addEventListener('click', () => {\n      this.closeSidebar();\n    });\n\n    // Retry button\n    const retryBtn = document.getElementById('retryBtn') as HTMLButtonElement;\n    retryBtn?.addEventListener('click', () => {\n      this.showWelcomeScreen();\n    });\n  }\n\n  private showWelcomeScreen(): void {\n    this.hideAllScreens();\n    const welcomeScreen = document.getElementById('welcomeScreen');\n    if (welcomeScreen) {\n      welcomeScreen.style.display = 'flex';\n    }\n  }\n\n  private showLoadingScreen(message: string = 'Analizando repositorio...'): void {\n    this.hideAllScreens();\n    const loadingScreen = document.getElementById('loadingScreen');\n    const loadingText = document.getElementById('loadingText');\n    \n    if (loadingScreen) {\n      loadingScreen.style.display = 'flex';\n    }\n    \n    if (loadingText) {\n      loadingText.textContent = message;\n    }\n  }\n\n  private showErrorScreen(message: string): void {\n    this.hideAllScreens();\n    const errorScreen = document.getElementById('errorScreen');\n    const errorMessage = document.getElementById('errorMessage');\n    \n    if (errorScreen) {\n      errorScreen.style.display = 'flex';\n    }\n    \n    if (errorMessage) {\n      errorMessage.textContent = message;\n    }\n  }\n\n  private showCommitTimeline(): void {\n    this.hideAllScreens();\n    const commitTimeline = document.getElementById('commitTimeline');\n    if (commitTimeline) {\n      commitTimeline.style.display = 'block';\n    }\n  }\n\n  private hideAllScreens(): void {\n    const screens = [\n      'welcomeScreen',\n      'loadingScreen', \n      'errorScreen',\n      'commitTimeline'\n    ];\n    \n    screens.forEach(screenId => {\n      const screen = document.getElementById(screenId);\n      if (screen) {\n        screen.style.display = 'none';\n      }\n    });\n  }\n\n  private async analyzeRepository(repositoryPath: string): Promise<void> {\n    if (this.isLoading) return;\n    \n    this.isLoading = true;\n    this.showLoadingScreen('Analizando repositorio...');\n\n    try {\n      const response = await fetch('/api/repository/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ repositoryPath }),\n      });\n\n      const result: ApiResponse<GitRepository> = await response.json();\n\n      if (!result.success || !result.data) {\n        throw new Error(result.error || 'Failed to analyze repository');\n      }\n\n      this.currentRepository = result.data;\n      this.updateRepositoryInfo();\n      await this.loadCommits(true);\n      \n    } catch (error) {\n      console.error('Error analyzing repository:', error);\n      this.showErrorScreen(error instanceof Error ? error.message : 'Error desconocido');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private updateRepositoryInfo(): void {\n    if (!this.currentRepository) return;\n\n    // Show repository info bar\n    const repoInfo = document.getElementById('repoInfo');\n    if (repoInfo) {\n      repoInfo.style.display = 'flex';\n    }\n\n    // Update repository path\n    const repoPath = document.getElementById('repoPath');\n    if (repoPath) {\n      repoPath.textContent = this.currentRepository.path;\n      repoPath.setAttribute('title', this.currentRepository.path);\n    }\n\n    // Update current branch\n    const currentBranch = document.getElementById('currentBranch');\n    if (currentBranch) {\n      currentBranch.textContent = this.currentRepository.currentBranch;\n    }\n\n    // Update repository status\n    const repoStatus = document.getElementById('repoStatus');\n    if (repoStatus) {\n      const statusSpan = repoStatus.querySelector('span');\n      const statusIcon = repoStatus.querySelector('i');\n      \n      if (this.currentRepository.status.modified.length > 0 || \n          this.currentRepository.status.not_added.length > 0) {\n        if (statusSpan) statusSpan.textContent = 'Modificado';\n        if (statusIcon) {\n          statusIcon.className = 'fas fa-circle';\n          statusIcon.style.color = 'var(--warning-color)';\n        }\n      } else {\n        if (statusSpan) statusSpan.textContent = 'Limpio';\n        if (statusIcon) {\n          statusIcon.className = 'fas fa-circle';\n          statusIcon.style.color = 'var(--success-color)';\n        }\n      }\n    }\n\n    // Update branch selector\n    this.updateBranchSelector();\n  }\n\n  private updateBranchSelector(): void {\n    if (!this.currentRepository) return;\n\n    const branchSelector = document.getElementById('branchSelector') as HTMLSelectElement;\n    if (!branchSelector) return;\n\n    // Clear existing options\n    branchSelector.innerHTML = '<option value=\\\"\\\">Seleccionar rama...</option>';\n\n    // Add branches\n    this.currentRepository.branches.forEach(branch => {\n      const option = document.createElement('option');\n      option.value = branch;\n      option.textContent = branch;\n      \n      if (branch === this.currentRepository!.currentBranch) {\n        option.selected = true;\n        this.currentBranch = branch;\n      }\n      \n      branchSelector.appendChild(option);\n    });\n  }\n\n  private async loadCommits(reset: boolean = false): Promise<void> {\n    if (!this.currentRepository || this.isLoading) return;\n\n    if (reset) {\n      this.commitOffset = 0;\n      this.currentCommits = [];\n    }\n\n    this.isLoading = true;\n    this.showLoadingScreen('Cargando commits...');\n\n    try {\n      const response = await fetch('/api/repository/commits', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          repositoryPath: this.currentRepository.path,\n          branch: this.currentBranch,\n          limit: this.commitLimit,\n          offset: this.commitOffset\n        }),\n      });\n\n      const result: ApiResponse<{commits: GitCommit[], graph: string}> = await response.json();\n\n      if (!result.success || !result.data) {\n        throw new Error(result.error || 'Failed to load commits');\n      }\n\n      if (reset) {\n        this.currentCommits = result.data.commits;\n      } else {\n        this.currentCommits.push(...result.data.commits);\n      }\n\n      this.commitOffset += result.data.commits.length;\n      this.updateCommitCount();\n      this.renderCommits();\n      this.showCommitTimeline();\n\n    } catch (error) {\n      console.error('Error loading commits:', error);\n      this.showErrorScreen(error instanceof Error ? error.message : 'Error cargando commits');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private async loadMoreCommits(): Promise<void> {\n    await this.loadCommits(false);\n  }\n\n  private updateCommitCount(): void {\n    const commitCount = document.getElementById('commitCount');\n    if (commitCount) {\n      const count = this.currentCommits.length;\n      commitCount.textContent = `${count} commit${count !== 1 ? 's' : ''}`;\n    }\n  }\n\n  private renderCommits(): void {\n    const container = document.getElementById('commitListView');\n    if (!container) return;\n\n    container.innerHTML = '';\n\n    this.currentCommits.forEach(commit => {\n      const commitElement = this.createCommitElement(commit);\n      container.appendChild(commitElement);\n    });\n  }\n\n  private createCommitElement(commit: GitCommit): HTMLElement {\n    const commitItem = document.createElement('div');\n    commitItem.className = 'commit-item';\n    commitItem.addEventListener('click', () => this.showCommitDetails(commit.hash));\n\n    const avatar = document.createElement('div');\n    avatar.className = 'commit-avatar';\n    avatar.textContent = commit.author.name.charAt(0).toUpperCase();\n    avatar.style.backgroundColor = this.getAvatarColor(commit.author.email);\n\n    const content = document.createElement('div');\n    content.className = 'commit-content';\n\n    const message = document.createElement('div');\n    message.className = 'commit-message';\n    message.textContent = commit.message;\n\n    const meta = document.createElement('div');\n    meta.className = 'commit-meta';\n\n    const author = document.createElement('span');\n    author.textContent = commit.author.name;\n\n    const date = document.createElement('span');\n    date.textContent = this.formatDate(commit.date);\n\n    const hash = document.createElement('span');\n    hash.className = 'commit-hash';\n    hash.textContent = commit.shortHash;\n\n    meta.appendChild(author);\n    meta.appendChild(date);\n    meta.appendChild(hash);\n\n    content.appendChild(message);\n    content.appendChild(meta);\n\n    commitItem.appendChild(avatar);\n    commitItem.appendChild(content);\n\n    // Add refs if available\n    if (commit.refs) {\n      const refs = document.createElement('div');\n      refs.className = 'commit-refs';\n      \n      const refParts = commit.refs.split(', ');\n      refParts.forEach(ref => {\n        if (ref.trim()) {\n          const refElement = document.createElement('span');\n          refElement.className = 'commit-ref';\n          \n          if (ref.includes('origin/')) {\n            refElement.classList.add('branch');\n            refElement.textContent = ref.replace('origin/', '');\n          } else if (ref.includes('tag:')) {\n            refElement.classList.add('tag');\n            refElement.textContent = ref.replace('tag: ', '');\n          } else {\n            refElement.textContent = ref;\n          }\n          \n          refs.appendChild(refElement);\n        }\n      });\n      \n      if (refs.children.length > 0) {\n        commitItem.appendChild(refs);\n      }\n    }\n\n    return commitItem;\n  }\n\n  private getAvatarColor(email: string): string {\n    const colors = [\n      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', \n      '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'\n    ];\n    \n    let hash = 0;\n    for (let i = 0; i < email.length; i++) {\n      hash = email.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    \n    return colors[Math.abs(hash) % colors.length];\n  }\n\n  private formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) {\n      return 'Hoy';\n    } else if (diffDays === 1) {\n      return 'Ayer';\n    } else if (diffDays < 7) {\n      return `Hace ${diffDays} días`;\n    } else {\n      return date.toLocaleDateString('es-ES', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  }\n\n  private async showCommitDetails(hash: string): Promise<void> {\n    if (!this.currentRepository) return;\n\n    try {\n      const response = await fetch(`/api/repository/commit/${hash}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          repositoryPath: this.currentRepository.path\n        }),\n      });\n\n      const result: ApiResponse<CommitDetailsResponse> = await response.json();\n\n      if (!result.success || !result.data) {\n        throw new Error(result.error || 'Failed to load commit details');\n      }\n\n      this.renderCommitDetails(result.data);\n      this.openSidebar();\n\n    } catch (error) {\n      console.error('Error loading commit details:', error);\n      this.showToast('Error cargando detalles del commit', 'error');\n    }\n  }\n\n  private renderCommitDetails(details: CommitDetailsResponse): void {\n    const content = document.getElementById('commitDetailsContent');\n    if (!content) return;\n\n    const commit = this.currentCommits.find(c => c.hash === details.hash);\n    if (!commit) return;\n\n    content.innerHTML = `\n      <div class=\\\"commit-detail-section\\\">\n        <div class=\\\"commit-detail-info\\\">\n          <div class=\\\"detail-row\\\">\n            <span class=\\\"detail-label\\\">Hash:</span>\n            <span class=\\\"commit-hash\\\">${commit.shortHash}</span>\n          </div>\n          <div class=\\\"detail-row\\\">\n            <span class=\\\"detail-label\\\">Autor:</span>\n            <span>${commit.author.name}</span>\n          </div>\n          <div class=\\\"detail-row\\\">\n            <span class=\\\"detail-label\\\">Email:</span>\n            <span>${commit.author.email}</span>\n          </div>\n          <div class=\\\"detail-row\\\">\n            <span class=\\\"detail-label\\\">Fecha:</span>\n            <span>${new Date(commit.date).toLocaleString('es-ES')}</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\\\"commit-detail-section\\\">\n        <h4><i class=\\\"fas fa-comment\\\"></i> Mensaje</h4>\n        <p>${commit.message}</p>\n      </div>\n\n      <div class=\\\"commit-detail-section\\\">\n        <h4><i class=\\\"fas fa-file-alt\\\"></i> Archivos modificados</h4>\n        <ul class=\\\"commit-files\\\">\n          ${details.files.map(file => `\n            <li>\n              <span class=\\\"file-status ${file.status.toLowerCase()}\\\">${this.getFileStatusIcon(file.status)}</span>\n              <span>${file.name}</span>\n            </li>\n          `).join('')}\n        </ul>\n      </div>\n    `;\n  }\n\n  private getFileStatusIcon(status: string): string {\n    switch (status) {\n      case 'A': return '+';\n      case 'M': return '~';\n      case 'D': return '-';\n      case 'R': return '→';\n      case 'C': return '©';\n      default: return '?';\n    }\n  }\n\n  private switchToListView(): void {\n    const listViewBtn = document.getElementById('listViewBtn');\n    const graphViewBtn = document.getElementById('graphViewBtn');\n    const listView = document.getElementById('commitListView');\n    const graphView = document.getElementById('commitGraphView');\n\n    listViewBtn?.classList.add('active');\n    graphViewBtn?.classList.remove('active');\n    \n    if (listView) listView.style.display = 'block';\n    if (graphView) graphView.style.display = 'none';\n  }\n\n  private switchToGraphView(): void {\n    const listViewBtn = document.getElementById('listViewBtn');\n    const graphViewBtn = document.getElementById('graphViewBtn');\n    const listView = document.getElementById('commitListView');\n    const graphView = document.getElementById('commitGraphView');\n\n    listViewBtn?.classList.remove('active');\n    graphViewBtn?.classList.add('active');\n    \n    if (listView) listView.style.display = 'none';\n    if (graphView) graphView.style.display = 'block';\n\n    this.renderCommitGraph();\n  }\n\n  private renderCommitGraph(): void {\n    const canvas = document.getElementById('commitCanvas') as HTMLCanvasElement;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    canvas.width = canvas.offsetWidth;\n    canvas.height = Math.max(600, this.currentCommits.length * 60);\n\n    // Clear canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Draw commits\n    this.currentCommits.forEach((commit, index) => {\n      const y = index * 60 + 30;\n      const x = 50;\n\n      // Draw commit node\n      ctx.beginPath();\n      ctx.arc(x, y, 8, 0, 2 * Math.PI);\n      ctx.fillStyle = this.getAvatarColor(commit.author.email);\n      ctx.fill();\n      ctx.strokeStyle = '#fff';\n      ctx.lineWidth = 2;\n      ctx.stroke();\n\n      // Draw commit line\n      if (index < this.currentCommits.length - 1) {\n        ctx.beginPath();\n        ctx.moveTo(x, y + 8);\n        ctx.lineTo(x, y + 52);\n        ctx.strokeStyle = '#dee2e6';\n        ctx.lineWidth = 2;\n        ctx.stroke();\n      }\n\n      // Draw commit info\n      ctx.fillStyle = '#212529';\n      ctx.font = '14px -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto';\n      ctx.fillText(commit.message, x + 20, y - 5);\n      \n      ctx.fillStyle = '#6c757d';\n      ctx.font = '12px -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto';\n      ctx.fillText(`${commit.author.name} • ${commit.shortHash}`, x + 20, y + 15);\n    });\n\n    // Add click handler for canvas\n    canvas.onclick = (event) => {\n      const rect = canvas.getBoundingClientRect();\n      const y = event.clientY - rect.top;\n      const commitIndex = Math.floor((y - 30) / 60);\n      \n      if (commitIndex >= 0 && commitIndex < this.currentCommits.length) {\n        this.showCommitDetails(this.currentCommits[commitIndex].hash);\n      }\n    };\n  }\n\n  private openSidebar(): void {\n    const sidebar = document.getElementById('commitDetailsSidebar');\n    if (sidebar) {\n      sidebar.style.display = 'flex';\n      setTimeout(() => {\n        sidebar.classList.add('open');\n      }, 10);\n    }\n  }\n\n  private closeSidebar(): void {\n    const sidebar = document.getElementById('commitDetailsSidebar');\n    if (sidebar) {\n      sidebar.classList.remove('open');\n      setTimeout(() => {\n        sidebar.style.display = 'none';\n      }, 300);\n    }\n  }\n\n  private showToast(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {\n    const container = document.getElementById('toastContainer');\n    if (!container) return;\n\n    const toast = document.createElement('div');\n    toast.className = `toast ${type}`;\n    toast.textContent = message;\n\n    container.appendChild(toast);\n\n    setTimeout(() => {\n      toast.remove();\n    }, 5000);\n  }\n}\n\n// Initialize the application when DOM is loaded\ndocument.addEventListener('DOMContentLoaded', () => {\n  new GitVisualViewer();\n});\n"], "names": ["GitVisualViewer", "constructor", "currentRepository", "currentCommits", "currentBranch", "isLoading", "commitOffset", "commitLimit", "this", "initializeEventListeners", "showWelcomeScreen", "selectRepoBtn", "document", "getElementById", "folderInput", "addEventListener", "click", "event", "target", "files", "length", "folderPath", "webkitRelativePath", "split", "fullPath", "replace", "repositoryPath", "path", "analyzeRepository", "branchSelector", "value", "loadCommits", "refreshBtn", "listViewBtn", "graphViewBtn", "switchToListView", "switchToGraphView", "loadMoreBtn", "loadMoreCommits", "closeSidebarBtn", "closeSidebar", "retryBtn", "hideAllScreens", "welcomeScreen", "style", "display", "showLoadingScreen", "message", "loadingScreen", "loadingText", "textContent", "showErrorScreen", "errorScreen", "errorMessage", "showCommitTimeline", "commitTimeline", "for<PERSON>ach", "screenId", "screen", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "result", "json", "success", "data", "Error", "error", "updateRepositoryInfo", "console", "repoInfo", "repoPath", "setAttribute", "repoStatus", "statusSpan", "querySelector", "statusIcon", "status", "modified", "not_added", "className", "color", "updateBranchSelector", "innerHTML", "branches", "branch", "option", "createElement", "selected", "append<PERSON><PERSON><PERSON>", "reset", "limit", "offset", "commits", "push", "updateCommitCount", "renderCommits", "commitCount", "count", "container", "commit", "commitElement", "createCommitElement", "commitItem", "showCommitDetails", "hash", "avatar", "author", "name", "char<PERSON>t", "toUpperCase", "backgroundColor", "getAvatarColor", "email", "content", "meta", "date", "formatDate", "shortHash", "refs", "ref", "trim", "refElement", "includes", "classList", "add", "children", "colors", "i", "charCodeAt", "Math", "abs", "dateString", "Date", "diffMs", "getTime", "diffDays", "floor", "toLocaleDateString", "year", "month", "day", "renderCommitDetails", "openSidebar", "showToast", "details", "find", "c", "toLocaleString", "map", "file", "toLowerCase", "getFileStatusIcon", "join", "listView", "graph<PERSON>iew", "remove", "renderCommitGraph", "canvas", "ctx", "getContext", "width", "offsetWidth", "height", "max", "clearRect", "index", "y", "beginPath", "arc", "PI", "fillStyle", "fill", "strokeStyle", "lineWidth", "stroke", "moveTo", "lineTo", "font", "fillText", "x", "onclick", "rect", "getBoundingClientRect", "clientY", "top", "commitIndex", "sidebar", "setTimeout", "type", "toast"], "sourceRoot": ""}
import { Router, Request, Response } from 'express';
import { GitService } from '../services/git.service';
import {
  ApiResponse,
  RepositoryAnalysisRequest,
  CommitsRequest,
  GitRepository,
  GitCommit,
  CommitDetailsResponse
} from '../types/git.types';

const router = Router();

// Analyze repository endpoint
router.post('/repository/analyze', async (req: Request<{}, ApiResponse<GitRepository>, RepositoryAnalysisRequest>, res: Response): Promise<void> => {
  try {
    const { repositoryPath } = req.body;

    if (!repositoryPath) {
      res.status(400).json({
        success: false,
        error: 'Repository path is required'
      });
      return;
    }

    // Validate repository
    const isValidRepo = await GitService.validateRepository(repositoryPath);
    if (!isValidRepo) {
      res.status(400).json({
        success: false,
        error: 'Selected path is not a valid git repository'
      });
      return;
    }

    // Get repository information
    const gitService = new GitService(repositoryPath);
    const repoInfo = await gitService.getRepositoryInfo();

    res.json({
      success: true,
      data: repoInfo
    });

  } catch (error) {
    console.error('Error analyzing repository:', error);
    res.status(500).json({
      success: false,
      error: `Failed to analyze repository: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

// Get commits endpoint
router.post('/repository/commits', async (req: Request<{}, ApiResponse<{commits: GitCommit[], graph: string}>, CommitsRequest>, res: Response): Promise<void> => {
  try {
    const { repositoryPath, branch = 'HEAD', limit = 50, offset = 0 } = req.body;

    if (!repositoryPath) {
      res.status(400).json({
        success: false,
        error: 'Repository path is required'
      });
      return;
    }

    const gitService = new GitService(repositoryPath);
    const result = await gitService.getCommitGraph(branch, limit);

    res.json({
      success: true,
      data: {
        commits: result.commits,
        graph: result.graph
      }
    });

  } catch (error) {
    console.error('Error getting commits:', error);
    res.status(500).json({
      success: false,
      error: `Failed to get commits: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

// Get commit details endpoint
router.post('/repository/commit/:hash', async (req: Request<{hash: string}, ApiResponse<CommitDetailsResponse>, RepositoryAnalysisRequest>, res: Response): Promise<void> => {
  try {
    const { repositoryPath } = req.body;
    const { hash } = req.params;

    if (!repositoryPath) {
      res.status(400).json({
        success: false,
        error: 'Repository path is required'
      });
      return;
    }

    if (!hash) {
      res.status(400).json({
        success: false,
        error: 'Commit hash is required'
      });
      return;
    }

    const gitService = new GitService(repositoryPath);
    const commitDetails = await gitService.getCommitDetails(hash);

    res.json({
      success: true,
      data: commitDetails
    });

  } catch (error) {
    console.error('Error getting commit details:', error);
    res.status(500).json({
      success: false,
      error: `Failed to get commit details: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

// Get branches endpoint
router.post('/repository/branches', async (req: Request<{}, ApiResponse<any>, RepositoryAnalysisRequest>, res: Response): Promise<void> => {
  try {
    const { repositoryPath } = req.body;

    if (!repositoryPath) {
      res.status(400).json({
        success: false,
        error: 'Repository path is required'
      });
      return;
    }

    const gitService = new GitService(repositoryPath);
    const branches = await gitService.getBranches();

    res.json({
      success: true,
      data: {
        current: branches.current,
        all: branches.all,
        branches: branches.branches
      }
    });

  } catch (error) {
    console.error('Error getting branches:', error);
    res.status(500).json({
      success: false,
      error: `Failed to get branches: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

export default router;

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Visual Viewer</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1><i class="fab fa-git-alt"></i> Git Visual Viewer</h1>
                <div class="header-actions">
                    <button id="selectRepoBtn" class="btn btn-primary">
                        <i class="fas fa-folder-open"></i> Seleccionar Repositorio
                    </button>
                    <input type="file" id="folderInput" webkitdirectory style="display: none;">
                </div>
            </div>
        </header>

        <!-- Repository Info -->
        <div id="repoInfo" class="repo-info" style="display: none;">
            <div class="repo-details">
                <div class="repo-path">
                    <i class="fas fa-folder"></i>
                    <span id="repoPath"></span>
                </div>
                <div class="repo-stats">
                    <span class="stat">
                        <i class="fas fa-code-branch"></i>
                        <span id="currentBranch"></span>
                    </span>
                    <span class="stat">
                        <i class="fas fa-history"></i>
                        <span id="commitCount">0 commits</span>
                    </span>
                </div>
            </div>
            <div class="repo-actions">
                <select id="branchSelector" class="branch-selector">
                    <option value="">Seleccionar rama...</option>
                </select>
                <button id="refreshBtn" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Welcome Screen -->
            <div id="welcomeScreen" class="welcome-screen">
                <div class="welcome-content">
                    <i class="fab fa-git-alt welcome-icon"></i>
                    <h2>Bienvenido a Git Visual Viewer</h2>
                    <p>Selecciona un repositorio Git para comenzar a visualizar su historial de commits</p>
                    <button class="btn btn-primary btn-large" onclick="document.getElementById('selectRepoBtn').click()">
                        <i class="fas fa-folder-open"></i> Seleccionar Repositorio
                    </button>
                </div>
            </div>

            <!-- Loading Screen -->
            <div id="loadingScreen" class="loading-screen" style="display: none;">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p>Analizando repositorio...</p>
                </div>
            </div>

            <!-- Commit Timeline -->
            <div id="commitTimeline" class="commit-timeline" style="display: none;">
                <div class="timeline-header">
                    <h3><i class="fas fa-history"></i> Historial de Commits</h3>
                    <div class="timeline-controls">
                        <button id="toggleGraphBtn" class="btn btn-secondary">
                            <i class="fas fa-project-diagram"></i> Vista Gráfica
                        </button>
                    </div>
                </div>
                <div id="commitsContainer" class="commits-container">
                    <!-- Commits will be loaded here -->
                </div>
            </div>

            <!-- Commit Details Panel -->
            <div id="commitDetails" class="commit-details" style="display: none;">
                <div class="details-header">
                    <h3><i class="fas fa-code-branch"></i> Detalles del Commit</h3>
                    <button id="closeDetailsBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="commitDetailsContent" class="details-content">
                    <!-- Commit details will be loaded here -->
                </div>
            </div>
        </main>

        <!-- Error Messages -->
        <div id="errorContainer" class="error-container" style="display: none;">
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorText"></span>
                <button id="closeErrorBtn" class="btn btn-small">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

import simpleGit, { SimpleG<PERSON>, <PERSON>g<PERSON><PERSON>ult, StatusR<PERSON>ult, BranchSummary } from 'simple-git';
import { promises as fs } from 'fs';
import path from 'path';
import {
  GitRepository,
  GitCommit,
  GitStatus,
  GitRemote,
  CommitDetailsResponse,
  CommitGraphNode
} from '../types/git.types';

export class GitService {
  private git: SimpleGit;

  constructor(repositoryPath: string) {
    this.git = simpleGit(repositoryPath);
  }

  static async validateRepository(repositoryPath: string): Promise<boolean> {
    try {
      await fs.access(repositoryPath);
      const git = simpleGit(repositoryPath);
      return await git.checkIsRepo();
    } catch (error) {
      return false;
    }
  }

  async getRepositoryInfo(): Promise<GitRepository> {
    try {
      const [status, branches, remotes] = await Promise.all([
        this.git.status(),
        this.git.branch(),
        this.git.getRemotes(true)
      ]);

      return {
        path: await this.git.revparse(['--show-toplevel']),
        currentBranch: branches.current,
        branches: branches.all,
        remotes: remotes.map(remote => ({
          name: remote.name,
          refs: remote.refs
        })) as GitRemote[],
        status: this.mapStatus(status)
      };
    } catch (error) {
      throw new Error(`Failed to get repository info: ${error}`);
    }
  }

  async getCommits(branch: string = 'HEAD', limit: number = 50, offset: number = 0): Promise<GitCommit[]> {
    try {
      const log = await this.git.log({
        from: branch,
        maxCount: limit,
        skip: offset,
        format: {
          hash: '%H',
          date: '%ai',
          message: '%s',
          author_name: '%an',
          author_email: '%ae',
          refs: '%D',
          parents: '%P'
        }
      });

      return log.all.map((commit: any) => ({
        hash: commit.hash,
        shortHash: commit.hash.substring(0, 7),
        message: commit.message,
        author: {
          name: commit.author_name,
          email: commit.author_email
        },
        date: commit.date,
        refs: commit.refs || '',
        parents: commit.parents ? commit.parents.split(' ') : []
      }));
    } catch (error) {
      throw new Error(`Failed to get commits: ${error}`);
    }
  }

  async getCommitDetails(hash: string): Promise<CommitDetailsResponse> {
    try {
      const [details, nameStatus] = await Promise.all([
        this.git.show([hash, '--stat', '--format=fuller']),
        this.git.raw(['show', '--name-status', '--format=', hash])
      ]);

      const files = this.parseFileChanges(nameStatus);

      return {
        hash,
        details,
        diff: await this.git.show([hash]),
        files
      };
    } catch (error) {
      throw new Error(`Failed to get commit details: ${error}`);
    }
  }

  async getBranches(): Promise<BranchSummary> {
    try {
      return await this.git.branch(['-a']);
    } catch (error) {
      throw new Error(`Failed to get branches: ${error}`);
    }
  }

  async getCommitGraph(branch: string = 'HEAD', limit: number = 50): Promise<{
    commits: GitCommit[];
    graph: string;
    nodes: CommitGraphNode[];
  }> {
    try {
      const [commits, graphOutput] = await Promise.all([
        this.getCommits(branch, limit),
        this.git.raw([
          'log',
          '--graph',
          '--pretty=format:%H|%P|%s|%an|%ai|%D',
          '--all',
          `--max-count=${limit}`
        ])
      ]);

      const nodes = this.parseGraphNodes(commits, graphOutput);

      return {
        commits,
        graph: graphOutput,
        nodes
      };
    } catch (error) {
      throw new Error(`Failed to get commit graph: ${error}`);
    }
  }

  private mapStatus(status: StatusResult): GitStatus {
    return {
      ahead: status.ahead,
      behind: status.behind,
      staged: status.staged,
      modified: status.modified,
      not_added: status.not_added,
      conflicted: status.conflicted,
      created: status.created,
      deleted: status.deleted,
      renamed: status.renamed.map(r => `${r.from} -> ${r.to}`)
    };
  }

  private parseFileChanges(nameStatus: string): CommitDetailsResponse['files'] {
    const lines = nameStatus.trim().split('\n').filter(line => line.trim());

    return lines.map(line => {
      const parts = line.trim().split('\t');
      const status = parts[0] as 'A' | 'M' | 'D' | 'R' | 'C';
      const name = parts[1];

      return {
        name,
        status
      };
    });
  }

  private parseGraphNodes(commits: GitCommit[], graphOutput: string): CommitGraphNode[] {
    const lines = graphOutput.split('\n');
    const nodes: CommitGraphNode[] = [];
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    let colorIndex = 0;

    commits.forEach((commit, index) => {
      const y = index * 60; // Vertical spacing
      const x = 50; // Base horizontal position

      nodes.push({
        commit,
        x,
        y,
        color: colors[colorIndex % colors.length],
        connections: []
      });

      colorIndex++;
    });

    return nodes;
  }
}

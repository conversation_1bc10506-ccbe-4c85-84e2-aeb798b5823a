<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Visual Viewer</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-git-alt"></i>
                    <h1>Git Visual Viewer</h1>
                </div>
                <div class="header-actions">
                    <button id="selectRepoBtn" class="btn btn-primary">
                        <i class="fas fa-folder-open"></i>
                        Seleccionar Repositorio
                    </button>
                    <input type="file" id="folderInput" webkitdirectory style="display: none;">
                </div>
            </div>
        </header>

        <!-- Repository Info Bar -->
        <div id="repoInfo" class="repo-info" style="display: none;">
            <div class="repo-details">
                <div class="repo-path">
                    <i class="fas fa-folder"></i>
                    <span id="repoPath" title=""></span>
                </div>
                <div class="repo-stats">
                    <span class="stat">
                        <i class="fas fa-code-branch"></i>
                        <span id="currentBranch"></span>
                    </span>
                    <span class="stat">
                        <i class="fas fa-history"></i>
                        <span id="commitCount">0 commits</span>
                    </span>
                    <span class="stat" id="repoStatus">
                        <i class="fas fa-circle"></i>
                        <span>Clean</span>
                    </span>
                </div>
            </div>
            <div class="repo-actions">
                <select id="branchSelector" class="branch-selector">
                    <option value="">Seleccionar rama...</option>
                </select>
                <button id="refreshBtn" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    Actualizar
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Welcome Screen -->
            <div id="welcomeScreen" class="welcome-screen">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fab fa-git-alt"></i>
                    </div>
                    <h2>Bienvenido a Git Visual Viewer</h2>
                    <p>Una herramienta visual para explorar repositorios Git de manera intuitiva, similar a GitKraken</p>
                    <div class="features">
                        <div class="feature">
                            <i class="fas fa-project-diagram"></i>
                            <span>Visualización gráfica de commits</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-code-branch"></i>
                            <span>Navegación por branches</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-history"></i>
                            <span>Timeline interactivo</span>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-large" onclick="document.getElementById('selectRepoBtn').click()">
                        <i class="fas fa-folder-open"></i>
                        Comenzar - Seleccionar Repositorio
                    </button>
                </div>
            </div>

            <!-- Loading Screen -->
            <div id="loadingScreen" class="loading-screen" style="display: none;">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p id="loadingText">Analizando repositorio...</p>
                </div>
            </div>

            <!-- Error Screen -->
            <div id="errorScreen" class="error-screen" style="display: none;">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error</h3>
                    <p id="errorMessage"></p>
                    <button id="retryBtn" class="btn btn-primary">
                        <i class="fas fa-redo"></i>
                        Intentar de nuevo
                    </button>
                </div>
            </div>

            <!-- Commit Timeline -->
            <div id="commitTimeline" class="commit-timeline" style="display: none;">
                <div class="timeline-header">
                    <h3>
                        <i class="fas fa-history"></i>
                        Historial de Commits
                    </h3>
                    <div class="timeline-controls">
                        <div class="view-toggle">
                            <button id="listViewBtn" class="btn btn-secondary active">
                                <i class="fas fa-list"></i>
                                Lista
                            </button>
                            <button id="graphViewBtn" class="btn btn-secondary">
                                <i class="fas fa-project-diagram"></i>
                                Gráfico
                            </button>
                        </div>
                        <button id="loadMoreBtn" class="btn btn-secondary">
                            <i class="fas fa-plus"></i>
                            Cargar más
                        </button>
                    </div>
                </div>

                <!-- Commit List View -->
                <div id="commitListView" class="commits-container">
                    <!-- Commits will be loaded here -->
                </div>

                <!-- Commit Graph View -->
                <div id="commitGraphView" class="commit-graph" style="display: none;">
                    <canvas id="commitCanvas"></canvas>
                </div>
            </div>
        </main>

        <!-- Commit Details Sidebar -->
        <aside id="commitDetailsSidebar" class="commit-details-sidebar" style="display: none;">
            <div class="sidebar-header">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    Detalles del Commit
                </h3>
                <button id="closeSidebarBtn" class="btn btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="commitDetailsContent" class="sidebar-content">
                <!-- Commit details will be loaded here -->
            </div>
        </aside>

        <!-- Toast Notifications -->
        <div id="toastContainer" class="toast-container"></div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>

const express = require('express');
const path = require('path');
const cors = require('cors');
const simpleGit = require('simple-git');
const fs = require('fs').promises;

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve static files
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoint to get repository information
app.post('/api/repository/analyze', async (req, res) => {
    try {
        const { repositoryPath } = req.body;
        
        if (!repositoryPath) {
            return res.status(400).json({ error: 'Repository path is required' });
        }

        // Check if path exists and is a git repository
        try {
            await fs.access(repositoryPath);
        } catch (error) {
            return res.status(404).json({ error: 'Repository path not found' });
        }

        const git = simpleGit(repositoryPath);
        
        // Check if it's a git repository
        const isRepo = await git.checkIsRepo();
        if (!isRepo) {
            return res.status(400).json({ error: 'Selected path is not a git repository' });
        }

        // Get repository information
        const status = await git.status();
        const branches = await git.branch();
        const remotes = await git.getRemotes(true);
        
        res.json({
            path: repositoryPath,
            currentBranch: branches.current,
            branches: branches.all,
            remotes: remotes,
            status: {
                ahead: status.ahead,
                behind: status.behind,
                staged: status.staged,
                modified: status.modified,
                not_added: status.not_added
            }
        });

    } catch (error) {
        console.error('Error analyzing repository:', error);
        res.status(500).json({ error: 'Failed to analyze repository: ' + error.message });
    }
});

// API endpoint to get commit history
app.post('/api/repository/commits', async (req, res) => {
    try {
        const { repositoryPath, branch = 'HEAD', limit = 50 } = req.body;
        
        if (!repositoryPath) {
            return res.status(400).json({ error: 'Repository path is required' });
        }

        const git = simpleGit(repositoryPath);
        
        // Get commit log
        const log = await git.log({
            from: branch,
            maxCount: limit,
            format: {
                hash: '%H',
                date: '%ai',
                message: '%s',
                author_name: '%an',
                author_email: '%ae',
                refs: '%D'
            }
        });

        // Get graph information for visual representation
        const graphLog = await git.raw([
            'log',
            '--graph',
            '--pretty=format:%H|%P|%s|%an|%ai|%D',
            '--all',
            `-${limit}`
        ]);

        const commits = log.all.map(commit => ({
            hash: commit.hash,
            shortHash: commit.hash.substring(0, 7),
            message: commit.message,
            author: {
                name: commit.author_name,
                email: commit.author_email
            },
            date: commit.date,
            refs: commit.refs || ''
        }));

        res.json({
            commits,
            graph: graphLog
        });

    } catch (error) {
        console.error('Error getting commits:', error);
        res.status(500).json({ error: 'Failed to get commits: ' + error.message });
    }
});

// API endpoint to get commit details
app.post('/api/repository/commit/:hash', async (req, res) => {
    try {
        const { repositoryPath } = req.body;
        const { hash } = req.params;
        
        if (!repositoryPath) {
            return res.status(400).json({ error: 'Repository path is required' });
        }

        const git = simpleGit(repositoryPath);
        
        // Get commit details
        const commit = await git.show([hash, '--stat', '--format=fuller']);
        const diff = await git.show([hash, '--name-status']);
        
        res.json({
            hash,
            details: commit,
            diff: diff
        });

    } catch (error) {
        console.error('Error getting commit details:', error);
        res.status(500).json({ error: 'Failed to get commit details: ' + error.message });
    }
});

// API endpoint to get branches
app.post('/api/repository/branches', async (req, res) => {
    try {
        const { repositoryPath } = req.body;
        
        if (!repositoryPath) {
            return res.status(400).json({ error: 'Repository path is required' });
        }

        const git = simpleGit(repositoryPath);
        const branches = await git.branch(['-a']);
        
        res.json({
            current: branches.current,
            all: branches.all,
            branches: branches.branches
        });

    } catch (error) {
        console.error('Error getting branches:', error);
        res.status(500).json({ error: 'Failed to get branches: ' + error.message });
    }
});

app.listen(PORT, () => {
    console.log(`🚀 Git Visual Viewer running on http://localhost:${PORT}`);
    console.log(`📁 Ready to analyze Git repositories!`);
});

{"name": "git-visual-viewer", "version": "1.0.0", "description": "Visual Git repository viewer similar to GitKraken built with TypeScript", "main": "dist/server.js", "scripts": {"build": "tsc && webpack --mode production", "build:server": "tsc", "build:client": "webpack --mode production", "dev": "concurrently \"tsc --watch\" \"webpack --mode development --watch\" \"nodemon dist/server.js\"", "dev:server": "tsc --watch", "dev:client": "webpack --mode development --watch", "start": "node dist/server.js", "clean": "<PERSON>raf dist public/js"}, "keywords": ["git", "viewer", "visual", "commits", "repository", "typescript"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "simple-git": "^3.19.1", "helmet": "^7.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.0", "typescript": "^5.3.2", "ts-loader": "^9.5.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "nodemon": "^3.0.1", "concurrently": "^8.2.2", "rimraf": "^5.0.5"}}
import { 
  GitRepository, 
  GitCommit, 
  ApiResponse, 
  CommitDetailsResponse 
} from '../types/git.types';

class GitVisualViewer {
  private currentRepository: GitRepository | null = null;
  private currentCommits: GitCommit[] = [];
  private currentBranch: string = 'HEAD';
  private isLoading: boolean = false;
  private commitOffset: number = 0;
  private readonly commitLimit: number = 50;

  constructor() {
    this.initializeEventListeners();
    this.showWelcomeScreen();
  }

  private initializeEventListeners(): void {
    // Repository selection
    const selectRepoBtn = document.getElementById('selectRepoBtn') as HTMLButtonElement;
    const folderInput = document.getElementById('folderInput') as HTMLInputElement;
    
    selectRepoBtn?.addEventListener('click', () => {
      folderInput?.click();
    });

    folderInput?.addEventListener('change', (event) => {
      const target = event.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const folderPath = target.files[0].webkitRelativePath.split('/')[0];
        const fullPath = target.files[0].webkitRelativePath.replace(folderPath + '/', '');
        // Get the parent directory path
        const repositoryPath = (target.files[0] as any).path?.replace('/' + fullPath, '') || folderPath;
        this.analyzeRepository(repositoryPath);
      }
    });

    // Branch selector
    const branchSelector = document.getElementById('branchSelector') as HTMLSelectElement;
    branchSelector?.addEventListener('change', (event) => {
      const target = event.target as HTMLSelectElement;
      if (target.value && this.currentRepository) {
        this.currentBranch = target.value;
        this.loadCommits(true);
      }
    });

    // Refresh button
    const refreshBtn = document.getElementById('refreshBtn') as HTMLButtonElement;
    refreshBtn?.addEventListener('click', () => {
      if (this.currentRepository) {
        this.analyzeRepository(this.currentRepository.path);
      }
    });

    // View toggle buttons
    const listViewBtn = document.getElementById('listViewBtn') as HTMLButtonElement;
    const graphViewBtn = document.getElementById('graphViewBtn') as HTMLButtonElement;
    
    listViewBtn?.addEventListener('click', () => {
      this.switchToListView();
    });
    
    graphViewBtn?.addEventListener('click', () => {
      this.switchToGraphView();
    });

    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn') as HTMLButtonElement;
    loadMoreBtn?.addEventListener('click', () => {
      this.loadMoreCommits();
    });

    // Sidebar close button
    const closeSidebarBtn = document.getElementById('closeSidebarBtn') as HTMLButtonElement;
    closeSidebarBtn?.addEventListener('click', () => {
      this.closeSidebar();
    });

    // Retry button
    const retryBtn = document.getElementById('retryBtn') as HTMLButtonElement;
    retryBtn?.addEventListener('click', () => {
      this.showWelcomeScreen();
    });
  }

  private showWelcomeScreen(): void {
    this.hideAllScreens();
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'flex';
    }
  }

  private showLoadingScreen(message: string = 'Analizando repositorio...'): void {
    this.hideAllScreens();
    const loadingScreen = document.getElementById('loadingScreen');
    const loadingText = document.getElementById('loadingText');
    
    if (loadingScreen) {
      loadingScreen.style.display = 'flex';
    }
    
    if (loadingText) {
      loadingText.textContent = message;
    }
  }

  private showErrorScreen(message: string): void {
    this.hideAllScreens();
    const errorScreen = document.getElementById('errorScreen');
    const errorMessage = document.getElementById('errorMessage');
    
    if (errorScreen) {
      errorScreen.style.display = 'flex';
    }
    
    if (errorMessage) {
      errorMessage.textContent = message;
    }
  }

  private showCommitTimeline(): void {
    this.hideAllScreens();
    const commitTimeline = document.getElementById('commitTimeline');
    if (commitTimeline) {
      commitTimeline.style.display = 'block';
    }
  }

  private hideAllScreens(): void {
    const screens = [
      'welcomeScreen',
      'loadingScreen', 
      'errorScreen',
      'commitTimeline'
    ];
    
    screens.forEach(screenId => {
      const screen = document.getElementById(screenId);
      if (screen) {
        screen.style.display = 'none';
      }
    });
  }

  private async analyzeRepository(repositoryPath: string): Promise<void> {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.showLoadingScreen('Analizando repositorio...');

    try {
      const response = await fetch('/api/repository/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ repositoryPath }),
      });

      const result: ApiResponse<GitRepository> = await response.json();

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to analyze repository');
      }

      this.currentRepository = result.data;
      this.updateRepositoryInfo();
      await this.loadCommits(true);
      
    } catch (error) {
      console.error('Error analyzing repository:', error);
      this.showErrorScreen(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      this.isLoading = false;
    }
  }

  private updateRepositoryInfo(): void {
    if (!this.currentRepository) return;

    // Show repository info bar
    const repoInfo = document.getElementById('repoInfo');
    if (repoInfo) {
      repoInfo.style.display = 'flex';
    }

    // Update repository path
    const repoPath = document.getElementById('repoPath');
    if (repoPath) {
      repoPath.textContent = this.currentRepository.path;
      repoPath.setAttribute('title', this.currentRepository.path);
    }

    // Update current branch
    const currentBranch = document.getElementById('currentBranch');
    if (currentBranch) {
      currentBranch.textContent = this.currentRepository.currentBranch;
    }

    // Update repository status
    const repoStatus = document.getElementById('repoStatus');
    if (repoStatus) {
      const statusSpan = repoStatus.querySelector('span');
      const statusIcon = repoStatus.querySelector('i');
      
      if (this.currentRepository.status.modified.length > 0 || 
          this.currentRepository.status.not_added.length > 0) {
        if (statusSpan) statusSpan.textContent = 'Modificado';
        if (statusIcon) {
          statusIcon.className = 'fas fa-circle';
          statusIcon.style.color = 'var(--warning-color)';
        }
      } else {
        if (statusSpan) statusSpan.textContent = 'Limpio';
        if (statusIcon) {
          statusIcon.className = 'fas fa-circle';
          statusIcon.style.color = 'var(--success-color)';
        }
      }
    }

    // Update branch selector
    this.updateBranchSelector();
  }

  private updateBranchSelector(): void {
    if (!this.currentRepository) return;

    const branchSelector = document.getElementById('branchSelector') as HTMLSelectElement;
    if (!branchSelector) return;

    // Clear existing options
    branchSelector.innerHTML = '<option value=\"\">Seleccionar rama...</option>';

    // Add branches
    this.currentRepository.branches.forEach(branch => {
      const option = document.createElement('option');
      option.value = branch;
      option.textContent = branch;
      
      if (branch === this.currentRepository!.currentBranch) {
        option.selected = true;
        this.currentBranch = branch;
      }
      
      branchSelector.appendChild(option);
    });
  }

  private async loadCommits(reset: boolean = false): Promise<void> {
    if (!this.currentRepository || this.isLoading) return;

    if (reset) {
      this.commitOffset = 0;
      this.currentCommits = [];
    }

    this.isLoading = true;
    this.showLoadingScreen('Cargando commits...');

    try {
      const response = await fetch('/api/repository/commits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repositoryPath: this.currentRepository.path,
          branch: this.currentBranch,
          limit: this.commitLimit,
          offset: this.commitOffset
        }),
      });

      const result: ApiResponse<{commits: GitCommit[], graph: string}> = await response.json();

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to load commits');
      }

      if (reset) {
        this.currentCommits = result.data.commits;
      } else {
        this.currentCommits.push(...result.data.commits);
      }

      this.commitOffset += result.data.commits.length;
      this.updateCommitCount();
      this.renderCommits();
      this.showCommitTimeline();

    } catch (error) {
      console.error('Error loading commits:', error);
      this.showErrorScreen(error instanceof Error ? error.message : 'Error cargando commits');
    } finally {
      this.isLoading = false;
    }
  }

  private async loadMoreCommits(): Promise<void> {
    await this.loadCommits(false);
  }

  private updateCommitCount(): void {
    const commitCount = document.getElementById('commitCount');
    if (commitCount) {
      const count = this.currentCommits.length;
      commitCount.textContent = `${count} commit${count !== 1 ? 's' : ''}`;
    }
  }

  private renderCommits(): void {
    const container = document.getElementById('commitListView');
    if (!container) return;

    container.innerHTML = '';

    this.currentCommits.forEach(commit => {
      const commitElement = this.createCommitElement(commit);
      container.appendChild(commitElement);
    });
  }

  private createCommitElement(commit: GitCommit): HTMLElement {
    const commitItem = document.createElement('div');
    commitItem.className = 'commit-item';
    commitItem.addEventListener('click', () => this.showCommitDetails(commit.hash));

    const avatar = document.createElement('div');
    avatar.className = 'commit-avatar';
    avatar.textContent = commit.author.name.charAt(0).toUpperCase();
    avatar.style.backgroundColor = this.getAvatarColor(commit.author.email);

    const content = document.createElement('div');
    content.className = 'commit-content';

    const message = document.createElement('div');
    message.className = 'commit-message';
    message.textContent = commit.message;

    const meta = document.createElement('div');
    meta.className = 'commit-meta';

    const author = document.createElement('span');
    author.textContent = commit.author.name;

    const date = document.createElement('span');
    date.textContent = this.formatDate(commit.date);

    const hash = document.createElement('span');
    hash.className = 'commit-hash';
    hash.textContent = commit.shortHash;

    meta.appendChild(author);
    meta.appendChild(date);
    meta.appendChild(hash);

    content.appendChild(message);
    content.appendChild(meta);

    commitItem.appendChild(avatar);
    commitItem.appendChild(content);

    // Add refs if available
    if (commit.refs) {
      const refs = document.createElement('div');
      refs.className = 'commit-refs';
      
      const refParts = commit.refs.split(', ');
      refParts.forEach(ref => {
        if (ref.trim()) {
          const refElement = document.createElement('span');
          refElement.className = 'commit-ref';
          
          if (ref.includes('origin/')) {
            refElement.classList.add('branch');
            refElement.textContent = ref.replace('origin/', '');
          } else if (ref.includes('tag:')) {
            refElement.classList.add('tag');
            refElement.textContent = ref.replace('tag: ', '');
          } else {
            refElement.textContent = ref;
          }
          
          refs.appendChild(refElement);
        }
      });
      
      if (refs.children.length > 0) {
        commitItem.appendChild(refs);
      }
    }

    return commitItem;
  }

  private getAvatarColor(email: string): string {
    const colors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', 
      '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'
    ];
    
    let hash = 0;
    for (let i = 0; i < email.length; i++) {
      hash = email.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  private formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Hoy';
    } else if (diffDays === 1) {
      return 'Ayer';
    } else if (diffDays < 7) {
      return `Hace ${diffDays} días`;
    } else {
      return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }

  private async showCommitDetails(hash: string): Promise<void> {
    if (!this.currentRepository) return;

    try {
      const response = await fetch(`/api/repository/commit/${hash}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repositoryPath: this.currentRepository.path
        }),
      });

      const result: ApiResponse<CommitDetailsResponse> = await response.json();

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to load commit details');
      }

      this.renderCommitDetails(result.data);
      this.openSidebar();

    } catch (error) {
      console.error('Error loading commit details:', error);
      this.showToast('Error cargando detalles del commit', 'error');
    }
  }

  private renderCommitDetails(details: CommitDetailsResponse): void {
    const content = document.getElementById('commitDetailsContent');
    if (!content) return;

    const commit = this.currentCommits.find(c => c.hash === details.hash);
    if (!commit) return;

    content.innerHTML = `
      <div class=\"commit-detail-section\">
        <div class=\"commit-detail-info\">
          <div class=\"detail-row\">
            <span class=\"detail-label\">Hash:</span>
            <span class=\"commit-hash\">${commit.shortHash}</span>
          </div>
          <div class=\"detail-row\">
            <span class=\"detail-label\">Autor:</span>
            <span>${commit.author.name}</span>
          </div>
          <div class=\"detail-row\">
            <span class=\"detail-label\">Email:</span>
            <span>${commit.author.email}</span>
          </div>
          <div class=\"detail-row\">
            <span class=\"detail-label\">Fecha:</span>
            <span>${new Date(commit.date).toLocaleString('es-ES')}</span>
          </div>
        </div>
      </div>

      <div class=\"commit-detail-section\">
        <h4><i class=\"fas fa-comment\"></i> Mensaje</h4>
        <p>${commit.message}</p>
      </div>

      <div class=\"commit-detail-section\">
        <h4><i class=\"fas fa-file-alt\"></i> Archivos modificados</h4>
        <ul class=\"commit-files\">
          ${details.files.map(file => `
            <li>
              <span class=\"file-status ${file.status.toLowerCase()}\">${this.getFileStatusIcon(file.status)}</span>
              <span>${file.name}</span>
            </li>
          `).join('')}
        </ul>
      </div>
    `;
  }

  private getFileStatusIcon(status: string): string {
    switch (status) {
      case 'A': return '+';
      case 'M': return '~';
      case 'D': return '-';
      case 'R': return '→';
      case 'C': return '©';
      default: return '?';
    }
  }

  private switchToListView(): void {
    const listViewBtn = document.getElementById('listViewBtn');
    const graphViewBtn = document.getElementById('graphViewBtn');
    const listView = document.getElementById('commitListView');
    const graphView = document.getElementById('commitGraphView');

    listViewBtn?.classList.add('active');
    graphViewBtn?.classList.remove('active');
    
    if (listView) listView.style.display = 'block';
    if (graphView) graphView.style.display = 'none';
  }

  private switchToGraphView(): void {
    const listViewBtn = document.getElementById('listViewBtn');
    const graphViewBtn = document.getElementById('graphViewBtn');
    const listView = document.getElementById('commitListView');
    const graphView = document.getElementById('commitGraphView');

    listViewBtn?.classList.remove('active');
    graphViewBtn?.classList.add('active');
    
    if (listView) listView.style.display = 'none';
    if (graphView) graphView.style.display = 'block';

    this.renderCommitGraph();
  }

  private renderCommitGraph(): void {
    const canvas = document.getElementById('commitCanvas') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = Math.max(600, this.currentCommits.length * 60);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw commits
    this.currentCommits.forEach((commit, index) => {
      const y = index * 60 + 30;
      const x = 50;

      // Draw commit node
      ctx.beginPath();
      ctx.arc(x, y, 8, 0, 2 * Math.PI);
      ctx.fillStyle = this.getAvatarColor(commit.author.email);
      ctx.fill();
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw commit line
      if (index < this.currentCommits.length - 1) {
        ctx.beginPath();
        ctx.moveTo(x, y + 8);
        ctx.lineTo(x, y + 52);
        ctx.strokeStyle = '#dee2e6';
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      // Draw commit info
      ctx.fillStyle = '#212529';
      ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
      ctx.fillText(commit.message, x + 20, y - 5);
      
      ctx.fillStyle = '#6c757d';
      ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
      ctx.fillText(`${commit.author.name} • ${commit.shortHash}`, x + 20, y + 15);
    });

    // Add click handler for canvas
    canvas.onclick = (event) => {
      const rect = canvas.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const commitIndex = Math.floor((y - 30) / 60);
      
      if (commitIndex >= 0 && commitIndex < this.currentCommits.length) {
        this.showCommitDetails(this.currentCommits[commitIndex].hash);
      }
    };
  }

  private openSidebar(): void {
    const sidebar = document.getElementById('commitDetailsSidebar');
    if (sidebar) {
      sidebar.style.display = 'flex';
      setTimeout(() => {
        sidebar.classList.add('open');
      }, 10);
    }
  }

  private closeSidebar(): void {
    const sidebar = document.getElementById('commitDetailsSidebar');
    if (sidebar) {
      sidebar.classList.remove('open');
      setTimeout(() => {
        sidebar.style.display = 'none';
      }, 300);
    }
  }

  private showToast(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
    const container = document.getElementById('toastContainer');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    container.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 5000);
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new GitVisualViewer();
});

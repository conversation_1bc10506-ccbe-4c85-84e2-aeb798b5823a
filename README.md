# Git Visual Viewer

Una aplicación web moderna para visualizar repositorios Git de manera intuitiva, similar a GitKraken, construida con TypeScript y Node.js.

## 🚀 Características

- **Visualización intuitiva**: Interfaz moderna y fácil de usar
- **Selector de repositorio**: Explorador de archivos integrado para seleccionar repositorios
- **Timeline de commits**: Vista cronológica de todos los commits
- **Vista gráfica**: Representación visual del historial de commits
- **Detalles de commits**: Panel lateral con información detallada de cada commit
- **Navegación por branches**: Cambio fácil entre diferentes ramas
- **Información del repositorio**: Estado actual, ramas y estadísticas
- **Responsive**: Funciona en dispositivos móviles y de escritorio

## 🛠️ Tecnologías

- **Backend**: Node.js + Express + TypeScript
- **Frontend**: TypeScript + HTML5 + CSS3
- **Git Integration**: simple-git
- **Build Tools**: Webpack + TypeScript Compiler
- **Styling**: CSS moderno con variables y flexbox/grid

## 📦 Instalación

1. **Clonar el repositorio**:
   ```bash
   git clone <repository-url>
   cd git-visual-viewer
   ```

2. **Instalar dependencias**:
   ```bash
   npm install
   ```

3. **Compilar el proyecto**:
   ```bash
   npm run build
   ```

4. **Iniciar la aplicación**:
   ```bash
   npm start
   ```

   La aplicación estará disponible en `http://localhost:3000`

## 🔧 Desarrollo

Para desarrollo con recarga automática:

```bash
npm run dev
```

Este comando ejecutará:
- Compilación de TypeScript en modo watch
- Webpack en modo desarrollo con watch
- Nodemon para reiniciar el servidor automáticamente

### Scripts disponibles

- `npm run build` - Compilar todo el proyecto
- `npm run build:server` - Compilar solo el servidor
- `npm run build:client` - Compilar solo el cliente
- `npm run dev` - Modo desarrollo con watch
- `npm run dev:server` - Watch solo del servidor
- `npm run dev:client` - Watch solo del cliente
- `npm start` - Iniciar la aplicación
- `npm run clean` - Limpiar archivos compilados

## 🎯 Uso

1. **Abrir la aplicación** en tu navegador
2. **Seleccionar repositorio** usando el botón "Seleccionar Repositorio"
3. **Explorar commits** en la vista de timeline
4. **Cambiar entre vistas** usando los botones Lista/Gráfico
5. **Ver detalles** haciendo clic en cualquier commit
6. **Navegar por branches** usando el selector de ramas

## 🏗️ Estructura del Proyecto

```
├── src/
│   ├── server.ts              # Servidor Express principal
│   ├── types/
│   │   └── git.types.ts       # Tipos TypeScript
│   ├── services/
│   │   └── git.service.ts     # Servicio para operaciones Git
│   ├── routes/
│   │   └── git.routes.ts      # Rutas de la API
│   └── client/
│       └── app.ts             # Aplicación frontend
├── public/
│   ├── index.html             # Página principal
│   └── styles/
│       └── main.css           # Estilos principales
├── dist/                      # Archivos compilados
├── package.json
├── tsconfig.json
├── webpack.config.js
└── README.md
```

## 🔌 API Endpoints

- `POST /api/repository/analyze` - Analizar un repositorio
- `POST /api/repository/commits` - Obtener commits
- `POST /api/repository/commit/:hash` - Obtener detalles de un commit
- `POST /api/repository/branches` - Obtener ramas
- `GET /health` - Health check

## 🎨 Características de la UI

- **Tema moderno**: Colores y tipografía profesional
- **Iconos**: Font Awesome para iconografía consistente
- **Animaciones**: Transiciones suaves y feedback visual
- **Responsive**: Adaptable a diferentes tamaños de pantalla
- **Accesibilidad**: Contraste adecuado y navegación por teclado

## 🚧 Próximas Características

- [ ] Comparación entre commits
- [ ] Búsqueda de commits
- [ ] Filtros por autor/fecha
- [ ] Exportar historial
- [ ] Temas personalizables
- [ ] Soporte para múltiples repositorios

## 🤝 Contribuir

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👨‍💻 Autor

**Adrian** - Desarrollador principal

---

⭐ Si te gusta este proyecto, ¡dale una estrella en GitHub!
